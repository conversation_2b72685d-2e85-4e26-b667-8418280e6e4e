import logging
import pandas as pd
import io
from fastapi import APIRouter, Body, Query, UploadFile, File, HTTPException
from fastapi.responses import StreamingResponse
from tortoise.expressions import Q
from app.controllers.test_case import test_case_controller
from app.schemas.base import Fail, Success, SuccessExtra
from app.schemas.test_case import TestCaseCreate, TestCaseUpdate, TestCaseCopy
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth

logger = logging.getLogger(__name__)
router = APIRouter()
public_router = APIRouter()  # 公开路由，不需要认证


@router.get("/list", summary="功能测试用例列表")
async def list_test_case(
        page: int = Query(1, description="页码"),
        page_size: int = Query(10, description="每页数量"),
        case_name: str = Query(None, description="用例名称，用于搜索"),
        case_level: str = Query(None, description="用例等级"),
        project_id: int = Query(None, description="项目ID"),
        module_id: int = Query(None, description="模块ID"),
        module_ids: str = Query(None, description="多个模块ID，逗号分隔"),
        status: str = Query(None, description="状态"),
        is_smoke: bool = Query(None, description="是否冒烟用例"),
        source: str = Query(None, description="来源")
):
    q = Q()
    if case_name:
        q &= Q(case_name__contains=case_name)
    if case_level:
        q &= Q(case_level=case_level)
    if project_id:
        q &= Q(project_id=project_id)
    if module_id:
        q &= Q(module_id=module_id)
    elif module_ids:
        # 支持多个模块ID查询
        try:
            module_id_list = [int(mid.strip()) for mid in module_ids.split(',') if mid.strip()]
            if module_id_list:
                q &= Q(module_id__in=module_id_list)
        except ValueError:
            pass  # 忽略无效的模块ID
    if status:
        q &= Q(status=status)
    if is_smoke is not None:
        q &= Q(is_smoke=is_smoke)
    if source:
        q &= Q(source=source)

    if project_id:
        total, data = await test_case_controller.list_by_project_with_user_info(
            project_id=project_id, page=page, page_size=page_size, search=q
        )
    else:
        total, data = await test_case_controller.list_with_user_info(
            page=page, page_size=page_size, search=q
        )

    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="获取功能测试用例详情")
async def get_test_case(
        test_case_id: int = Query(..., description="测试用例ID"),
):
    test_case_obj = await test_case_controller.get(id=test_case_id)
    test_case_dict = await test_case_obj.to_dict()
    return Success(data=test_case_dict)


@router.post("/create", summary="创建功能测试用例", dependencies=[DependAuth])
async def create_test_case(
        test_case_in: TestCaseCreate,
):
    user_id = CTX_USER_ID.get()
    await test_case_controller.create_with_case_number(obj_in=test_case_in, user_id=user_id)
    return Success(msg="功能测试用例创建成功")


@router.post("/update", summary="更新功能测试用例", dependencies=[DependAuth])
async def update_test_case(
        test_case_in: TestCaseUpdate = Body(...),
):
    await test_case_controller.update(id=test_case_in.id, obj_in=test_case_in)
    return Success(msg="功能测试用例更新成功")


@router.delete("/delete", summary="删除功能测试用例", dependencies=[DependAuth])
async def delete_test_case(
        id: int = Query(..., description="测试用例ID"),
):
    await test_case_controller.remove(id=id)
    return Success(msg="功能测试用例删除成功")


@router.post("/copy", summary="复制功能测试用例", dependencies=[DependAuth])
async def copy_test_case(
        copy_data: TestCaseCopy = Body(...),
):
    user_id = CTX_USER_ID.get()
    await test_case_controller.copy_test_case(copy_data=copy_data, user_id=user_id)
    return Success(msg="功能测试用例复制成功")


@router.post("/reset", summary="重置功能测试用例编号", dependencies=[DependAuth])
async def reset_test_case_numbers(
        request_data: dict = Body(..., description="重置请求数据")
):
    """重置指定项目的测试用例编号"""
    try:
        project_id = request_data.get("project_id")
        if not project_id:
            return Fail(msg="缺少project_id参数")

        from app.models.admin import Project

        # 获取项目信息
        project = await Project.get(id=project_id)
        project_initials = test_case_controller.get_project_initials(project.name)

        # 获取该项目下的所有测试用例，按创建时间排序
        test_cases = await test_case_controller.model.filter(project_id=project_id).order_by('created_at').all()

        # 重新分配编号
        for index, test_case in enumerate(test_cases, 1):
            new_case_number = f"{project_initials}_{index}"
            await test_case.update_from_dict({"case_number": new_case_number}).save()

        return Success(msg=f"成功重置 {len(test_cases)} 个测试用例的编号")

    except Exception as e:
        logger.error(f"重置测试用例编号失败: {str(e)}")
        return Fail(msg=f"重置失败: {str(e)}")


@public_router.get("/download_template", summary="下载功能测试用例导入模板")
async def download_test_case_template():
    """下载功能测试用例导入模板"""
    try:
        # 创建模板数据
        template_data = {
            '需求ID': ['REQ001', 'REQ002'],
            '所属模块': ['用户管理', '订单管理'],
            '用例名称': ['用户登录功能测试', '订单创建功能测试'],
            '用例等级': ['高', '中'],
            '前置条件': ['用户已注册', '用户已登录'],
            '用例步骤': ['1. 打开登录页面\n2. 输入用户名和密码\n3. 点击登录按钮', '1. 进入订单页面\n2. 填写订单信息\n3. 提交订单'],
            '预期结果': ['用户成功登录，跳转到首页', '订单创建成功，显示订单详情'],
            '是否冒烟用例': ['是', '否']
        }

        # 创建DataFrame
        df = pd.DataFrame(template_data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='功能测试用例模板', index=False)

            # 获取工作表对象，设置列宽
            worksheet = writer.sheets['功能测试用例模板']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        output.seek(0)

        # 返回文件
        import urllib.parse
        filename = urllib.parse.quote("功能测试用例导入模板.xlsx".encode('utf-8'))
        headers = {
            'Content-Disposition': f'attachment; filename*=UTF-8\'\'{filename}'
        }

        return StreamingResponse(
            io.BytesIO(output.read()),
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers=headers
        )

    except Exception as e:
        logger.error(f"下载模板失败: {str(e)}")
        return Fail(msg=f"下载模板失败: {str(e)}")


@router.post("/import", summary="导入功能测试用例", dependencies=[DependAuth])
async def import_test_cases(
        project_id: int = Body(..., description="项目ID"),
        module_id: int = Body(None, description="默认模块ID"),
        file: UploadFile = File(..., description="Excel文件")
):
    """导入功能测试用例"""
    try:
        user_id = CTX_USER_ID.get()

        # 检查文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            return Fail(msg="请上传Excel文件（.xlsx或.xls格式）")

        # 读取Excel文件
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))

        # 验证必需的列
        required_columns = ['用例名称', '用例等级', '用例步骤', '预期结果', '是否冒烟用例']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return Fail(msg=f"缺少必需的列: {', '.join(missing_columns)}")

        # 数据验证和导入
        success_count = 0
        error_messages = []

        # 获取项目信息用于生成用例编号
        from app.models.admin import Project, ProjectModule
        project = await Project.get(id=project_id)

        # 获取项目下的所有模块，用于模块名称匹配
        project_modules = {}
        if module_id is None:  # 只有在没有指定默认模块时才需要查找模块
            modules = await ProjectModule.filter(project_id=project_id).all()
            for module in modules:
                project_modules[module.name] = module.id

        for index, row in df.iterrows():
            try:
                # 验证用例等级
                case_level_map = {'高': 'high', '中': 'medium', '低': 'low'}
                case_level_value = str(row['用例等级']).strip()
                if case_level_value not in case_level_map:
                    error_messages.append(f"第{index+2}行：用例等级必须是'高'、'中'或'低'，当前值：{case_level_value}")
                    continue

                # 验证是否冒烟用例
                is_smoke_map = {'是': True, '否': False}
                is_smoke_value = str(row['是否冒烟用例']).strip()
                if is_smoke_value not in is_smoke_map:
                    error_messages.append(f"第{index+2}行：是否冒烟用例必须是'是'或'否'，当前值：{is_smoke_value}")
                    continue

                # 验证必填字段
                if pd.isna(row['用例名称']) or str(row['用例名称']).strip() == '':
                    error_messages.append(f"第{index+2}行：用例名称不能为空")
                    continue

                if pd.isna(row['用例步骤']) or str(row['用例步骤']).strip() == '':
                    error_messages.append(f"第{index+2}行：用例步骤不能为空")
                    continue

                if pd.isna(row['预期结果']) or str(row['预期结果']).strip() == '':
                    error_messages.append(f"第{index+2}行：预期结果不能为空")
                    continue

                # 处理模块分配
                case_module_id = module_id  # 使用默认模块
                if module_id is None and '所属模块' in row and not pd.isna(row['所属模块']):
                    # 如果没有指定默认模块，尝试根据模块名称查找
                    module_name = str(row['所属模块']).strip()
                    if module_name in project_modules:
                        case_module_id = project_modules[module_name]
                    else:
                        # 模块名称不存在时给出警告，但不阻止导入
                        error_messages.append(f"第{index+2}行：模块'{module_name}'不存在，将不分配模块")

                # 创建测试用例数据
                test_case_data = TestCaseCreate(
                    case_name=str(row['用例名称']).strip(),
                    case_level=case_level_map[case_level_value],
                    precondition=str(row.get('前置条件', '')).strip() if not pd.isna(row.get('前置条件')) else '',
                    test_steps=str(row['用例步骤']).strip(),
                    expected_result=str(row['预期结果']).strip(),
                    is_smoke=is_smoke_map[is_smoke_value],
                    status='pending',  # 导入的用例默认为待审核
                    source='manual',   # 来源为人工
                    project_id=project_id,
                    module_id=case_module_id
                )

                # 创建测试用例
                await test_case_controller.create_with_case_number(obj_in=test_case_data, user_id=user_id)
                success_count += 1

            except Exception as e:
                error_messages.append(f"第{index+2}行：导入失败 - {str(e)}")
                continue

        # 返回导入结果
        if success_count > 0:
            result_msg = f"成功导入 {success_count} 条测试用例"
            if error_messages:
                result_msg += f"，{len(error_messages)} 条失败"
                return Success(data={
                    "success_count": success_count,
                    "error_count": len(error_messages),
                    "error_messages": error_messages[:10]  # 只返回前10条错误信息
                }, msg=result_msg)
            else:
                return Success(data={"success_count": success_count, "error_count": 0}, msg=result_msg)
        else:
            return Fail(msg="导入失败，请检查数据格式", data={"error_messages": error_messages[:10]})

    except Exception as e:
        logger.error(f"导入测试用例失败: {str(e)}")
        return Fail(msg=f"导入失败: {str(e)}")
